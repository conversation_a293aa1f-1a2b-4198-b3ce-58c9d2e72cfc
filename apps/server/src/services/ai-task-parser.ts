import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { generateObject } from 'ai';
import { z } from 'zod';
import logger, { measureTime } from '../lib/logger';
import { DateParser } from '../lib/date-parser';
import { 
  parsedTasksResponseSchema, 
  autoPlanResponseSchema,
  validateParsedTask,
  convertPriorityToScore,
  type ParsedTask,
  type ParsedTasksResponse,
  type AutoPlanResponse
} from '../lib/schemas';
import { db, tasks, milestones } from '../db';
import { eq } from 'drizzle-orm';

export interface AITaskParserConfig {
  maxRetries: number;
  retryDelay: number;
  confidenceThreshold: number;
  includeContext: boolean;
}

export interface UserContext {
  userId: string;
  recentTasks: any[];
  milestones: any[];
  preferences?: {
    defaultPriority?: number;
    workingHours?: { start: string; end: string };
    timezone?: string;
  };
}

export class AITaskParserError extends Error {
  constructor(
    message: string,
    public type: 'AI_PARSING_ERROR' | 'VALIDATION_ERROR' | 'CONTEXT_ERROR' | 'RATE_LIMIT_ERROR',
    public retryable: boolean = false,
    public details?: any
  ) {
    super(message);
    this.name = 'AITaskParserError';
  }
}

export class AITaskParser {
  private openrouter;
  private config: AITaskParserConfig;

  constructor(config: Partial<AITaskParserConfig> = {}) {
    this.openrouter = createOpenRouter({
      apiKey: process.env.OPENROUTER_API_KEY,
    });

    this.config = {
      maxRetries: 3,
      retryDelay: 1000,
      confidenceThreshold: 0.7,
      includeContext: true,
      ...config,
    };

    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }
  }

  async parseTasksFromNaturalLanguage(
    input: string,
    userContext: UserContext
  ): Promise<ParsedTasksResponse> {
    const requestId = logger.generateRequestId();
    
    logger.aiParsingStart(input, { 
      requestId, 
      userId: userContext.userId,
      operation: 'parse_tasks'
    });

    return measureTime('ai_task_parsing', async () => {
      let lastError: Error | null = null;

      for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
        try {
          if (attempt > 1) {
            logger.aiRetryAttempt(attempt, this.config.maxRetries, { 
              requestId, 
              userId: userContext.userId 
            });
            await this.delay(this.config.retryDelay * Math.pow(2, attempt - 1));
          }

          const prompt = await this.buildTaskParsingPrompt(input, userContext);
          
          logger.debug('Sending request to AI model', {
            requestId,
            userId: userContext.userId,
            operation: 'ai_request',
            metadata: { 
              attempt,
              promptLength: prompt.length,
              model: 'google/gemini-2.0-flash-exp:free'
            }
          });

          const result = await generateObject({
            model: this.openrouter.chat('google/gemini-2.0-flash-exp:free'),
            schema: parsedTasksResponseSchema,
            prompt,
          });

          const parsedResponse = result.object;
          
          // Validate and enhance the response
          const validatedResponse = await this.validateAndEnhanceResponse(
            parsedResponse, 
            input, 
            userContext
          );

          logger.aiParsingSuccess(validatedResponse, { 
            requestId, 
            userId: userContext.userId 
          });

          return validatedResponse;

        } catch (error) {
          lastError = error as Error;
          
          logger.error(`AI parsing attempt ${attempt} failed`, {
            requestId,
            userId: userContext.userId,
            operation: 'ai_parsing',
            metadata: { attempt, maxRetries: this.config.maxRetries }
          }, error as Error);

          // Check if error is retryable
          if (!this.isRetryableError(error as Error) || attempt === this.config.maxRetries) {
            break;
          }
        }
      }

      // All retries failed
      logger.aiParsingError(lastError!, input, { 
        requestId, 
        userId: userContext.userId 
      });

      throw new AITaskParserError(
        `Failed to parse tasks after ${this.config.maxRetries} attempts: ${lastError?.message}`,
        'AI_PARSING_ERROR',
        false,
        { originalError: lastError, input }
      );
    }, { requestId, userId: userContext.userId });
  }

  async generateAutoPlan(
    taskTitle: string,
    taskDescription: string | undefined,
    context: string | undefined,
    userContext: UserContext
  ): Promise<AutoPlanResponse> {
    const requestId = logger.generateRequestId();
    
    logger.info('Starting auto-plan generation', {
      requestId,
      userId: userContext.userId,
      operation: 'auto_plan',
      metadata: { taskTitle, hasDescription: !!taskDescription, hasContext: !!context }
    });

    return measureTime('ai_auto_plan', async () => {
      const prompt = await this.buildAutoPlanPrompt(taskTitle, taskDescription, context, userContext);
      
      const result = await generateObject({
        model: this.openrouter.chat('google/gemini-2.0-flash-exp:free'),
        schema: autoPlanResponseSchema,
        prompt,
      });

      logger.info('Auto-plan generation completed', {
        requestId,
        userId: userContext.userId,
        operation: 'auto_plan',
        metadata: { 
          subtasksCount: result.object.subtasks.length,
          confidence: result.object.confidence
        }
      });

      return result.object;
    }, { requestId, userId: userContext.userId });
  }

  private async buildTaskParsingPrompt(input: string, userContext: UserContext): Promise<string> {
    const contextInfo = this.config.includeContext ? await this.buildUserContext(userContext) : '';
    
    // Extract potential dates from input
    const extractedDates = DateParser.extractDatesFromText(input);
    const dateContext = extractedDates.length > 0 
      ? `\nDetected potential dates: ${extractedDates.map(d => `"${d.originalText}" -> ${d.interpretation} (${DateParser.formatDateForAI(d.date)})`).join(', ')}`
      : '';

    return `
Parse the following natural language input into structured tasks for a solo founder's to-do list.

Input: "${input}"
${dateContext}
${contextInfo}

Guidelines:
- Extract clear, actionable tasks with specific titles
- Infer due dates from time expressions (use ISO format YYYY-MM-DD)
- Estimate time requirements when possible (be realistic)
- Assign priority based on urgency and importance (1=low, 5=high)
- Break down complex requests into multiple tasks if appropriate
- For sequential tasks (e.g., "do X then Y"), create separate tasks
- Add relevant tags for categorization
- Identify task dependencies when mentioned
- Provide reasoning for your parsing decisions
- Flag any ambiguities or potential issues

Examples:
- "Finish Stripe onboarding, then email investors tomorrow" → 
  Task 1: "Complete Stripe onboarding setup", priority: 4, tags: ["payment", "setup"]
  Task 2: "Email update to investors", dueDate: tomorrow, priority: 4, tags: ["communication", "investors"], dependencies: ["Complete Stripe onboarding setup"]

- "Schedule dentist appointment for next week" →
  Task 1: "Schedule dentist appointment", dueDate: next week, priority: 2, tags: ["health", "appointment"]

Return tasks that are specific, actionable, and helpful for a busy founder. Include confidence level and reasoning.
    `.trim();
  }

  private async buildAutoPlanPrompt(
    taskTitle: string, 
    taskDescription: string | undefined, 
    context: string | undefined, 
    userContext: UserContext
  ): Promise<string> {
    const contextInfo = this.config.includeContext ? await this.buildUserContext(userContext) : '';

    return `
Create an Auto Plan for the following task by breaking it down into actionable subtasks.

Task: "${taskTitle}"
Description: "${taskDescription || 'No additional description provided'}"
Context: "${context || 'No additional context provided'}"
${contextInfo}

Guidelines:
- Break down the main task into specific, actionable subtasks
- Estimate realistic time requirements for each subtask
- Assign appropriate priorities (1=low, 5=high)
- Suggest relevant tools or services when helpful
- Identify dependencies between subtasks
- Add relevant tags for organization
- Define clear success criteria
- Provide helpful tips and identify potential risks
- Ensure subtasks are ordered logically

Focus on creating a practical, executable plan that a solo founder can follow step by step.
    `.trim();
  }

  private async buildUserContext(userContext: UserContext): Promise<string> {
    let context = '\nUser Context:';
    
    if (userContext.recentTasks.length > 0) {
      const recentTaskTitles = userContext.recentTasks
        .slice(0, 5)
        .map(task => `- ${task.title}`)
        .join('\n');
      context += `\nRecent tasks:\n${recentTaskTitles}`;
    }

    if (userContext.milestones.length > 0) {
      const milestoneInfo = userContext.milestones
        .slice(0, 3)
        .map(milestone => `- ${milestone.title} (weight: ${milestone.weight})`)
        .join('\n');
      context += `\nCurrent milestones:\n${milestoneInfo}`;
    }

    if (userContext.preferences) {
      context += `\nPreferences: ${JSON.stringify(userContext.preferences)}`;
    }

    return context;
  }

  private async validateAndEnhanceResponse(
    response: ParsedTasksResponse,
    originalInput: string,
    userContext: UserContext
  ): Promise<ParsedTasksResponse> {
    const validatedTasks: ParsedTask[] = [];
    const warnings: string[] = [...(response.warnings || [])];

    for (const task of response.tasks) {
      const validation = validateParsedTask(task);
      
      if (!validation.isValid) {
        logger.validationError(validation.errors, task, {
          userId: userContext.userId,
          operation: 'task_validation'
        });
        warnings.push(...validation.errors);
      }

      // Enhance task with additional processing
      const enhancedTask = await this.enhanceTask(task, userContext);
      validatedTasks.push(enhancedTask);
    }

    // Check confidence threshold
    if (response.confidence < this.config.confidenceThreshold) {
      warnings.push(`Low confidence score (${response.confidence}). Please review the parsed tasks carefully.`);
    }

    return {
      ...response,
      tasks: validatedTasks,
      warnings,
    };
  }

  private async enhanceTask(task: ParsedTask, userContext: UserContext): Promise<ParsedTask> {
    // Enhance due date parsing
    if (task.dueDate && !DateParser.isValidDate(task.dueDate)) {
      const parsedDate = DateParser.parseNaturalLanguageDate(task.dueDate);
      if (parsedDate) {
        task.dueDate = DateParser.formatDateForAI(parsedDate.date);
      }
    }

    // Add default priority if missing
    if (!task.priority) {
      task.priority = userContext.preferences?.defaultPriority || 3;
    }

    return task;
  }

  private isRetryableError(error: Error): boolean {
    const retryablePatterns = [
      /rate limit/i,
      /timeout/i,
      /network/i,
      /connection/i,
      /temporary/i,
      /503/,
      /502/,
      /500/,
    ];

    return retryablePatterns.some(pattern => pattern.test(error.message));
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getUserContext(userId: string): Promise<UserContext> {
    try {
      // Get recent tasks
      const recentTasks = await db.select()
        .from(tasks)
        .where(eq(tasks.userId, parseInt(userId)))
        .orderBy(tasks.createdAt)
        .limit(10);

      // Get milestones
      const userMilestones = await db.select()
        .from(milestones)
        .where(eq(milestones.userId, parseInt(userId)))
        .orderBy(milestones.weight);

      return {
        userId,
        recentTasks,
        milestones: userMilestones,
        preferences: {
          defaultPriority: 3,
          workingHours: { start: '09:00', end: '17:00' },
          timezone: 'UTC',
        },
      };
    } catch (error) {
      logger.error('Failed to build user context', {
        userId,
        operation: 'user_context'
      }, error as Error);

      // Return minimal context on error
      return {
        userId,
        recentTasks: [],
        milestones: [],
      };
    }
  }
}
