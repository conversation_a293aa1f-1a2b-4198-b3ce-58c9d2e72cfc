import { z } from "zod";
import {
  publicProcedure,
  protectedProcedure,
  router,
} from "../lib/trpc";
import { db, tasks, milestones, users } from "../db";
import { eq, and, desc, asc, isNull } from "drizzle-orm";
import { <PERSON><PERSON><PERSON>Parser, AITaskParserError } from "../services/ai-task-parser";
import logger, { withRequestContext, measureTime } from "../lib/logger";
import {
  createTaskSchema,
  updateTaskSchema,
  parseTasksInputSchema,
  autoPlanInputSchema,
  convertPriorityToScore,
  type ParsedTask,
} from "../lib/schemas";

// Milestone schemas (tasks schemas imported from shared lib)
const createMilestoneSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  targetDate: z.string().optional(),
  weight: z.number().int().min(1).max(5).default(3),
});

const updateMilestoneSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  targetDate: z.string().optional(),
  weight: z.number().int().min(1).max(5).optional(),
});

// Initialize AI Task Parser
const aiTaskParser = new AITaskParser({
  maxRetries: 3,
  retryDelay: 1000,
  confidenceThreshold: 0.7,
  includeContext: true,
});

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    console.log("🏥 Health check called - no context needed");
    return "OK";
  }),

  // Simple test endpoint without database
  test: publicProcedure.query(() => {
    console.log("🧪 Test endpoint called - no context needed");
    return { message: "Test successful", timestamp: new Date().toISOString() };
  }),

  // Test with context but no auth check
  contextTest: publicProcedure.query(({ ctx }) => {
    console.log("🔧 Context test called:", {
      hasUser: !!ctx.user,
      hasSession: !!ctx.session,
      userInfo: ctx.user ? { id: ctx.user.id, name: ctx.user.name } : null
    });
    return {
      message: "Context test successful",
      hasUser: !!ctx.user,
      hasSession: !!ctx.session,
      timestamp: new Date().toISOString()
    };
  }),

  // Task Management Procedures
  tasks: router({
    // Create a new task
    create: protectedProcedure
      .input(createTaskSchema)
      .mutation(async ({ input, ctx }) => {
        const [newTask] = await db.insert(tasks).values({
          ...input,
          userId: ctx.user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        }).returning();
        
        return newTask;
      }),

    // Get all tasks for user with optional filtering
    list: protectedProcedure
      .input(z.object({
        status: z.enum(["todo", "doing", "done"]).optional(),
        isHighlight: z.boolean().optional(),
        parentTaskId: z.string().uuid().optional(),
        includeSubtasks: z.boolean().default(true),
      }).optional())
      .query(async ({ input = {}, ctx }) => {
        // Apply filters
        const conditions = [eq(tasks.userId, ctx.user.id)];
        
        if (input.status) {
          conditions.push(eq(tasks.status, input.status));
        }
        
        if (input.isHighlight !== undefined) {
          conditions.push(eq(tasks.isHighlight, input.isHighlight));
        }
        
        if (input.parentTaskId) {
          conditions.push(eq(tasks.parentTaskId, input.parentTaskId));
        } else if (!input.includeSubtasks) {
          conditions.push(isNull(tasks.parentTaskId));
        }
        
        const result = await db.select().from(tasks)
          .where(and(...conditions))
          .orderBy(desc(tasks.createdAt));
        
        return result;
      }),

    // Get a single task by ID
    getById: protectedProcedure
      .input(z.object({ id: z.string().uuid() }))
      .query(async ({ input, ctx }) => {
        const [task] = await db.select().from(tasks)
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)));
        
        if (!task) {
          throw new Error("Task not found");
        }
        
        return task;
      }),

    // Update a task
    update: protectedProcedure
      .input(updateTaskSchema)
      .mutation(async ({ input, ctx }) => {
        const { id, ...updateData } = input;
        
        const [updatedTask] = await db.update(tasks)
          .set({
            ...updateData,
            updatedAt: new Date(),
          })
          .where(and(eq(tasks.id, id), eq(tasks.userId, ctx.user.id)))
          .returning();
        
        if (!updatedTask) {
          throw new Error("Task not found or unauthorized");
        }
        
        return updatedTask;
      }),

    // Delete a task
    delete: protectedProcedure
      .input(z.object({ id: z.string().uuid() }))
      .mutation(async ({ input, ctx }) => {
        const [deletedTask] = await db.delete(tasks)
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)))
          .returning();
        
        if (!deletedTask) {
          throw new Error("Task not found or unauthorized");
        }
        
        return { success: true, id: input.id };
      }),

    // Get task hierarchy (parent with all subtasks)
    getHierarchy: protectedProcedure
      .input(z.object({ id: z.string().uuid() }))
      .query(async ({ input, ctx }) => {
        // Get the main task
        const [mainTask] = await db.select().from(tasks)
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)));
        
        if (!mainTask) {
          throw new Error("Task not found");
        }
        
        // Get all subtasks
        const subtasks = await db.select().from(tasks)
          .where(and(eq(tasks.parentTaskId, input.id), eq(tasks.userId, ctx.user.id)))
          .orderBy(asc(tasks.createdAt));
        
        return {
          ...mainTask,
          subtasks,
        };
      }),

    // Create multiple tasks (for AI-generated subtasks)
    createMany: protectedProcedure
      .input(z.object({
        tasks: z.array(createTaskSchema),
      }))
      .mutation(async ({ input, ctx }) => {
        const tasksToInsert = input.tasks.map(task => ({
          ...task,
          userId: ctx.user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        }));
        
        const newTasks = await db.insert(tasks).values(tasksToInsert).returning();
        
        return newTasks;
      }),

    // Update task status
    updateStatus: protectedProcedure
      .input(z.object({
        id: z.string().uuid(),
        status: z.enum(["todo", "doing", "done"]),
      }))
      .mutation(async ({ input, ctx }) => {
        const [updatedTask] = await db.update(tasks)
          .set({
            status: input.status,
            updatedAt: new Date(),
          })
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)))
          .returning();
        
        if (!updatedTask) {
          throw new Error("Task not found or unauthorized");
        }
        
        return updatedTask;
      }),

    // Toggle highlight status
    toggleHighlight: protectedProcedure
      .input(z.object({ id: z.string().uuid() }))
      .mutation(async ({ input, ctx }) => {
        // First get current state
        const [currentTask] = await db.select().from(tasks)
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)));
        
        if (!currentTask) {
          throw new Error("Task not found");
        }
        
        const [updatedTask] = await db.update(tasks)
          .set({
            isHighlight: !currentTask.isHighlight,
            updatedAt: new Date(),
          })
          .where(and(eq(tasks.id, input.id), eq(tasks.userId, ctx.user.id)))
          .returning();
        
        return updatedTask;
      }),

    // Auto-plan: Generate AI subtasks and save to database
    autoPlan: protectedProcedure
      .input(autoPlanInputSchema)
      .mutation(withRequestContext(async ({ input, ctx }) => {
        const requestId = logger.generateRequestId();

        logger.userAction('auto_plan_requested', {
          requestId,
          userId: ctx.user.id.toString(),
          metadata: {
            taskTitle: input.taskTitle,
            hasDescription: !!input.taskDescription,
            hasContext: !!input.context,
            saveToDatabase: input.saveToDatabase
          }
        });

        try {
          // Get user context for AI planning
          const userContext = await aiTaskParser.getUserContext(ctx.user.id.toString());

          // Generate auto plan using AI service
          const plan = await aiTaskParser.generateAutoPlan(
            input.taskTitle,
            input.taskDescription,
            input.context,
            userContext
          );

          if (input.saveToDatabase) {
            // Create the main task if taskId is not provided
            let parentTaskId = input.taskId;

            if (!parentTaskId) {
              const [newMainTask] = await db.insert(tasks).values({
                title: input.taskTitle,
                description: input.taskDescription || '',
                userId: ctx.user.id,
                priorityScore: 50, // Default priority
                status: 'todo',
                createdAt: new Date(),
                updatedAt: new Date(),
              }).returning();

              parentTaskId = newMainTask.id;

              logger.info('Main task created for auto-plan', {
                requestId,
                userId: ctx.user.id.toString(),
                operation: 'main_task_created',
                metadata: { taskId: parentTaskId, title: input.taskTitle }
              });
            }

            // Create subtasks in database with improved mapping
            const subtasksToInsert = plan.subtasks.map((subtask, index) => ({
              title: subtask.title,
              description: subtask.description,
              parentTaskId,
              userId: ctx.user.id,
              priorityScore: convertPriorityToScore(subtask.priority),
              status: 'todo' as const,
              toolAction: {
                tool: subtask.suggestedTool,
                estimatedTime: subtask.estimatedTime,
                dependencies: subtask.dependencies || [],
                tags: subtask.tags || []
              },
              createdAt: new Date(),
              updatedAt: new Date(),
            }));

            const createdSubtasks = await db.insert(tasks).values(subtasksToInsert).returning();

            logger.info('Auto-plan subtasks created successfully', {
              requestId,
              userId: ctx.user.id.toString(),
              operation: 'subtasks_created',
              metadata: {
                subtasksCount: createdSubtasks.length,
                parentTaskId,
                confidence: plan.confidence
              }
            });

            return {
              ...plan,
              parentTaskId,
              subtasks: plan.subtasks.map((subtask, index) => ({
                ...subtask,
                id: createdSubtasks[index].id,
                created: true,
              })),
            };
          }

          return plan;
        } catch (error) {
          logger.error('Auto-plan generation failed', {
            requestId,
            userId: ctx.user.id.toString(),
            operation: 'auto_plan',
            metadata: { taskTitle: input.taskTitle }
          }, error as Error);

          throw new Error(`Failed to generate auto plan: ${(error as Error).message}`);
        }
      })),

    // Parse natural language input into structured tasks
    parseTasks: protectedProcedure
      .input(parseTasksInputSchema)
      .mutation(withRequestContext(async ({ input, ctx }) => {
        const requestId = logger.generateRequestId();

        logger.userAction('parse_tasks_requested', {
          requestId,
          userId: ctx.user.id.toString(),
          metadata: {
            inputLength: input.input.length,
            saveToDatabase: input.saveToDatabase,
            includeContext: input.includeContext
          }
        });

        try {
          // Get user context for AI parsing
          const userContext = await aiTaskParser.getUserContext(ctx.user.id.toString());

          // Parse tasks using AI service
          const parsedResponse = await aiTaskParser.parseTasksFromNaturalLanguage(
            input.input,
            userContext
          );

          if (input.saveToDatabase && parsedResponse.tasks.length > 0) {
            // Create tasks in database with improved mapping
            const tasksToInsert = parsedResponse.tasks.map(task => ({
              title: task.title,
              description: task.description || '',
              dueDate: task.dueDate || null,
              userId: ctx.user.id,
              priorityScore: task.priority ? convertPriorityToScore(task.priority) : 50,
              status: 'todo' as const,
              toolAction: task.estimatedTime ? {
                estimatedTime: task.estimatedTime,
                tags: task.tags || [],
                dependencies: task.dependencies || []
              } : null,
              createdAt: new Date(),
              updatedAt: new Date(),
            }));

            const createdTasks = await db.insert(tasks).values(tasksToInsert).returning();

            logger.info('Tasks created successfully', {
              requestId,
              userId: ctx.user.id.toString(),
              operation: 'tasks_created',
              metadata: {
                tasksCount: createdTasks.length,
                confidence: parsedResponse.confidence
              }
            });

            return {
              ...parsedResponse,
              tasks: parsedResponse.tasks.map((task, index) => ({
                ...task,
                id: createdTasks[index].id,
                created: true,
              })),
            };
          }

          return parsedResponse;
        } catch (error) {
          if (error instanceof AITaskParserError) {
            logger.error('AI Task Parser Error', {
              requestId,
              userId: ctx.user.id.toString(),
              operation: 'parse_tasks',
              metadata: {
                errorType: error.type,
                retryable: error.retryable
              }
            }, error);

            throw new Error(`Task parsing failed: ${error.message}`);
          }

          logger.error('Unexpected error in parseTasks', {
            requestId,
            userId: ctx.user.id.toString(),
            operation: 'parse_tasks'
          }, error as Error);

          throw new Error(`Failed to parse tasks: ${(error as Error).message}`);
        }
      })),
  }),

  // Milestone Management Procedures
  milestones: router({
    // Create a milestone
    create: protectedProcedure
      .input(createMilestoneSchema)
      .mutation(async ({ input, ctx }) => {
        const [newMilestone] = await db.insert(milestones).values({
          ...input,
          userId: ctx.user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        }).returning();
        
        return newMilestone;
      }),

    // List all milestones for user
    list: protectedProcedure
      .query(async ({ ctx }) => {
        const result = await db.select().from(milestones)
          .where(eq(milestones.userId, ctx.user.id))
          .orderBy(desc(milestones.createdAt));
        
        return result;
      }),

    // Update a milestone
    update: protectedProcedure
      .input(z.object({
        id: z.string().uuid(),
        title: z.string().min(1).max(255).optional(),
        description: z.string().optional(),
        targetDate: z.string().optional(),
        weight: z.number().int().min(1).max(5).optional(),
      }))
      .mutation(async ({ input, ctx }) => {
        const { id, ...updateData } = input;
        
        const [updatedMilestone] = await db.update(milestones)
          .set({
            ...updateData,
            updatedAt: new Date(),
          })
          .where(and(eq(milestones.id, id), eq(milestones.userId, ctx.user.id)))
          .returning();
        
        if (!updatedMilestone) {
          throw new Error("Milestone not found or unauthorized");
        }
        
        return updatedMilestone;
      }),

    // Delete a milestone
    delete: protectedProcedure
      .input(z.object({ id: z.string().uuid() }))
      .mutation(async ({ input, ctx }) => {
        const [deletedMilestone] = await db.delete(milestones)
          .where(and(eq(milestones.id, input.id), eq(milestones.userId, ctx.user.id)))
          .returning();
        
        if (!deletedMilestone) {
          throw new Error("Milestone not found or unauthorized");
        }
        
        return { success: true, id: input.id };
      }),
  }),
});

export type AppRouter = typeof appRouter;
