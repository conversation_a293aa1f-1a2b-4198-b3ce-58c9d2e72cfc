import { z } from "zod";

// Base task schemas
export const createTaskSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  dueDate: z.string().optional(), // ISO date string
  parentTaskId: z.string().uuid().optional(),
  priorityScore: z.number().int().min(0).max(100).default(0),
  isHighlight: z.boolean().default(false),
  status: z.enum(["todo", "doing", "done"]).default("todo"),
  toolAction: z.record(z.any()).optional(),
});

export const updateTaskSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  dueDate: z.string().optional(),
  priorityScore: z.number().int().min(0).max(100).optional(),
  isHighlight: z.boolean().optional(),
  status: z.enum(["todo", "doing", "done"]).optional(),
  toolAction: z.record(z.any()).optional(),
});

// AI parsing schemas
export const parsedTaskSchema = z.object({
  title: z.string().describe('Clear, actionable task title'),
  description: z.string().optional().describe('Additional context or details'),
  dueDate: z.string().optional().describe('Due date in ISO format if mentioned'),
  estimatedTime: z.string().optional().describe('Estimated time to complete (e.g., "2 hours", "30 minutes")'),
  priority: z.number().min(1).max(5).optional().describe('Priority level from 1 (low) to 5 (high)'),
  tags: z.array(z.string()).optional().describe('Relevant tags or categories'),
  dependencies: z.array(z.string()).optional().describe('Tasks this depends on'),
});

export const parsedTasksResponseSchema = z.object({
  tasks: z.array(parsedTaskSchema),
  confidence: z.number().min(0).max(1).describe('Confidence level of the parsing'),
  reasoning: z.string().optional().describe('Explanation of parsing decisions'),
  warnings: z.array(z.string()).optional().describe('Potential issues or ambiguities'),
});

// Auto-plan schemas
export const subtaskSchema = z.object({
  title: z.string().describe('Clear, actionable subtask title'),
  description: z.string().describe('Detailed description of what needs to be done'),
  estimatedTime: z.string().describe('Estimated time to complete (e.g., "30 minutes", "2 hours")'),
  priority: z.number().min(1).max(5).describe('Priority level from 1 (low) to 5 (high)'),
  suggestedTool: z.string().optional().describe('Suggested tool or service to help with this task'),
  dependencies: z.array(z.string()).optional().describe('List of subtask titles this depends on'),
  tags: z.array(z.string()).optional().describe('Relevant tags or categories'),
});

export const autoPlanResponseSchema = z.object({
  subtasks: z.array(subtaskSchema),
  estimatedTotalTime: z.string().describe('Total estimated time for all subtasks'),
  successCriteria: z.array(z.string()).describe('How to know when the main task is complete'),
  tips: z.array(z.string()).optional().describe('Additional tips or considerations'),
  risks: z.array(z.string()).optional().describe('Potential risks or blockers'),
  confidence: z.number().min(0).max(1).describe('Confidence level of the plan'),
});

// Input validation schemas
export const parseTasksInputSchema = z.object({
  input: z.string().min(1).max(2000),
  saveToDatabase: z.boolean().default(true),
  includeContext: z.boolean().default(true),
});

export const autoPlanInputSchema = z.object({
  taskId: z.string().uuid().optional(),
  taskTitle: z.string().min(1).max(255),
  taskDescription: z.string().optional(),
  context: z.string().optional(),
  saveToDatabase: z.boolean().default(true),
});

// Error schemas
export const aiErrorSchema = z.object({
  type: z.enum(['AI_PARSING_ERROR', 'VALIDATION_ERROR', 'CONTEXT_ERROR', 'RATE_LIMIT_ERROR']),
  message: z.string(),
  details: z.record(z.any()).optional(),
  retryable: z.boolean(),
  timestamp: z.date(),
});

// Validation helpers
export const validateParsedTask = (task: z.infer<typeof parsedTaskSchema>) => {
  const errors: string[] = [];
  
  // Check title quality
  if (task.title.length < 3) {
    errors.push('Task title is too short');
  }
  
  // Check due date validity
  if (task.dueDate) {
    const date = new Date(task.dueDate);
    if (isNaN(date.getTime())) {
      errors.push('Invalid due date format');
    } else if (date < new Date()) {
      errors.push('Due date is in the past');
    }
  }
  
  // Check priority reasonableness
  if (task.priority && (task.priority < 1 || task.priority > 5)) {
    errors.push('Priority must be between 1 and 5');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Type exports
export type CreateTaskInput = z.infer<typeof createTaskSchema>;
export type UpdateTaskInput = z.infer<typeof updateTaskSchema>;
export type ParsedTask = z.infer<typeof parsedTaskSchema>;
export type ParsedTasksResponse = z.infer<typeof parsedTasksResponseSchema>;
export type Subtask = z.infer<typeof subtaskSchema>;
export type AutoPlanResponse = z.infer<typeof autoPlanResponseSchema>;
export type ParseTasksInput = z.infer<typeof parseTasksInputSchema>;
export type AutoPlanInput = z.infer<typeof autoPlanInputSchema>;
export type AIError = z.infer<typeof aiErrorSchema>;

// Priority conversion utilities
export const convertPriorityToScore = (priority: number): number => {
  // Convert 1-5 scale to 0-100 scale with better distribution
  const mapping = {
    1: 20,  // Low
    2: 35,  // Medium-Low
    3: 50,  // Medium
    4: 70,  // Medium-High
    5: 90,  // High
  };
  return mapping[priority as keyof typeof mapping] || 50;
};

export const convertScoreToPriority = (score: number): number => {
  if (score <= 25) return 1;
  if (score <= 40) return 2;
  if (score <= 60) return 3;
  if (score <= 80) return 4;
  return 5;
};
