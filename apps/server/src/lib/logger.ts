import { randomUUID } from 'crypto';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogContext {
  requestId?: string;
  userId?: string;
  operation?: string;
  metadata?: Record<string, any>;
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context: LogContext;
  error?: Error;
  data?: any;
}

class Logger {
  private currentLevel: LogLevel;
  private requestId: string | null = null;

  constructor(level: LogLevel = LogLevel.INFO) {
    this.currentLevel = level;
  }

  setRequestId(id: string) {
    this.requestId = id;
  }

  generateRequestId(): string {
    const id = randomUUID();
    this.setRequestId(id);
    return id;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.currentLevel;
  }

  private formatLog(entry: LogEntry): string {
    const { timestamp, level, message, context, error, data } = entry;
    const levelName = LogLevel[level];
    
    let logString = `[${timestamp}] ${levelName}: ${message}`;
    
    if (context.requestId) {
      logString += ` [RequestID: ${context.requestId}]`;
    }
    
    if (context.userId) {
      logString += ` [UserID: ${context.userId}]`;
    }
    
    if (context.operation) {
      logString += ` [Operation: ${context.operation}]`;
    }
    
    if (context.metadata && Object.keys(context.metadata).length > 0) {
      logString += ` [Metadata: ${JSON.stringify(context.metadata)}]`;
    }
    
    if (data) {
      logString += ` [Data: ${JSON.stringify(data, null, 2)}]`;
    }
    
    if (error) {
      logString += `\n  Error: ${error.message}`;
      if (error.stack) {
        logString += `\n  Stack: ${error.stack}`;
      }
    }
    
    return logString;
  }

  private log(level: LogLevel, message: string, context: LogContext = {}, error?: Error, data?: any) {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: {
        requestId: this.requestId || context.requestId,
        ...context,
      },
      error,
      data,
    };

    const formattedLog = this.formatLog(entry);
    
    // Output to appropriate console method
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedLog);
        break;
      case LogLevel.INFO:
        console.info(formattedLog);
        break;
      case LogLevel.WARN:
        console.warn(formattedLog);
        break;
      case LogLevel.ERROR:
        console.error(formattedLog);
        break;
    }
  }

  debug(message: string, context?: LogContext, data?: any) {
    this.log(LogLevel.DEBUG, message, context, undefined, data);
  }

  info(message: string, context?: LogContext, data?: any) {
    this.log(LogLevel.INFO, message, context, undefined, data);
  }

  warn(message: string, context?: LogContext, data?: any) {
    this.log(LogLevel.WARN, message, context, undefined, data);
  }

  error(message: string, context?: LogContext, error?: Error, data?: any) {
    this.log(LogLevel.ERROR, message, context, error, data);
  }

  // AI-specific logging methods
  aiParsingStart(input: string, context: LogContext = {}) {
    this.info('AI parsing started', {
      ...context,
      operation: 'ai_parsing',
      metadata: {
        inputLength: input.length,
        inputPreview: input.substring(0, 100),
      },
    });
  }

  aiParsingSuccess(result: any, context: LogContext = {}) {
    this.info('AI parsing completed successfully', {
      ...context,
      operation: 'ai_parsing',
      metadata: {
        tasksCount: result.tasks?.length || 0,
        confidence: result.confidence,
      },
    }, result);
  }

  aiParsingError(error: Error, input: string, context: LogContext = {}) {
    this.error('AI parsing failed', {
      ...context,
      operation: 'ai_parsing',
      metadata: {
        inputLength: input.length,
        inputPreview: input.substring(0, 100),
      },
    }, error);
  }

  aiRetryAttempt(attempt: number, maxAttempts: number, context: LogContext = {}) {
    this.warn(`AI parsing retry attempt ${attempt}/${maxAttempts}`, {
      ...context,
      operation: 'ai_parsing_retry',
      metadata: { attempt, maxAttempts },
    });
  }

  validationError(errors: string[], data: any, context: LogContext = {}) {
    this.warn('Validation errors found', {
      ...context,
      operation: 'validation',
      metadata: { errorCount: errors.length, errors },
    }, data);
  }

  performanceMetric(operation: string, duration: number, context: LogContext = {}) {
    this.info(`Performance metric: ${operation}`, {
      ...context,
      operation: 'performance',
      metadata: { duration, operation },
    });
  }

  userAction(action: string, context: LogContext = {}) {
    this.info(`User action: ${action}`, {
      ...context,
      operation: 'user_action',
      metadata: { action },
    });
  }
}

// Create singleton instance
const logger = new Logger(
  process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO
);

export default logger;

// Utility function to measure execution time
export const measureTime = async <T>(
  operation: string,
  fn: () => Promise<T>,
  context: LogContext = {}
): Promise<T> => {
  const start = Date.now();
  logger.debug(`Starting operation: ${operation}`, context);
  
  try {
    const result = await fn();
    const duration = Date.now() - start;
    logger.performanceMetric(operation, duration, context);
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    logger.error(`Operation failed: ${operation}`, context, error as Error, { duration });
    throw error;
  }
};

// Request context middleware helper
export const withRequestContext = <T extends any[], R>(
  fn: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<R> => {
    const requestId = logger.generateRequestId();
    logger.debug('Request started', { requestId });
    
    try {
      const result = await fn(...args);
      logger.debug('Request completed successfully', { requestId });
      return result;
    } catch (error) {
      logger.error('Request failed', { requestId }, error as Error);
      throw error;
    }
  };
};
