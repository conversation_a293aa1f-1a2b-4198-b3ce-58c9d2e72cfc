import logger from './logger';

export interface ParsedDate {
  date: Date;
  confidence: number;
  originalText: string;
  interpretation: string;
}

export class DateParser {
  private static readonly RELATIVE_PATTERNS = [
    // Today/Tomorrow/Yesterday
    { pattern: /\b(today|now)\b/i, days: 0 },
    { pattern: /\btomorrow\b/i, days: 1 },
    { pattern: /\byesterday\b/i, days: -1 },
    
    // This/Next week patterns
    { pattern: /\bthis\s+week\b/i, days: 3 }, // Middle of current week
    { pattern: /\bnext\s+week\b/i, days: 7 },
    { pattern: /\bin\s+a\s+week\b/i, days: 7 },
    
    // Days of week
    { pattern: /\bnext\s+monday\b/i, dayOfWeek: 1, next: true },
    { pattern: /\bnext\s+tuesday\b/i, dayOfWeek: 2, next: true },
    { pattern: /\bnext\s+wednesday\b/i, dayOfWeek: 3, next: true },
    { pattern: /\bnext\s+thursday\b/i, dayOfWeek: 4, next: true },
    { pattern: /\bnext\s+friday\b/i, dayOfWeek: 5, next: true },
    { pattern: /\bnext\s+saturday\b/i, dayOfWeek: 6, next: true },
    { pattern: /\bnext\s+sunday\b/i, dayOfWeek: 0, next: true },
    
    { pattern: /\bmonday\b/i, dayOfWeek: 1 },
    { pattern: /\btuesday\b/i, dayOfWeek: 2 },
    { pattern: /\bwednesday\b/i, dayOfWeek: 3 },
    { pattern: /\bthursday\b/i, dayOfWeek: 4 },
    { pattern: /\bfriday\b/i, dayOfWeek: 5 },
    { pattern: /\bsaturday\b/i, dayOfWeek: 6 },
    { pattern: /\bsunday\b/i, dayOfWeek: 0 },
    
    // Relative days
    { pattern: /\bin\s+(\d+)\s+days?\b/i, daysFromMatch: true },
    { pattern: /\b(\d+)\s+days?\s+from\s+now\b/i, daysFromMatch: true },
    
    // Month patterns
    { pattern: /\bnext\s+month\b/i, months: 1 },
    { pattern: /\bin\s+a\s+month\b/i, months: 1 },
  ];

  private static readonly ABSOLUTE_PATTERNS = [
    // ISO dates
    { pattern: /\b(\d{4})-(\d{1,2})-(\d{1,2})\b/, format: 'YYYY-MM-DD' },
    
    // US format
    { pattern: /\b(\d{1,2})\/(\d{1,2})\/(\d{4})\b/, format: 'MM/DD/YYYY' },
    { pattern: /\b(\d{1,2})\/(\d{1,2})\/(\d{2})\b/, format: 'MM/DD/YY' },
    
    // European format
    { pattern: /\b(\d{1,2})\.(\d{1,2})\.(\d{4})\b/, format: 'DD.MM.YYYY' },
    
    // Month names
    { pattern: /\b(january|jan)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 1 },
    { pattern: /\b(february|feb)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 2 },
    { pattern: /\b(march|mar)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 3 },
    { pattern: /\b(april|apr)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 4 },
    { pattern: /\b(may)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 5 },
    { pattern: /\b(june|jun)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 6 },
    { pattern: /\b(july|jul)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 7 },
    { pattern: /\b(august|aug)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 8 },
    { pattern: /\b(september|sep|sept)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 9 },
    { pattern: /\b(october|oct)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 10 },
    { pattern: /\b(november|nov)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 11 },
    { pattern: /\b(december|dec)\s+(\d{1,2}),?\s*(\d{4})?\b/i, month: 12 },
  ];

  static parseNaturalLanguageDate(text: string, baseDate: Date = new Date()): ParsedDate | null {
    const normalizedText = text.toLowerCase().trim();
    
    logger.debug('Parsing natural language date', {
      operation: 'date_parsing',
      metadata: { text: normalizedText, baseDate: baseDate.toISOString() }
    });

    // Try relative patterns first
    for (const pattern of this.RELATIVE_PATTERNS) {
      const match = normalizedText.match(pattern.pattern);
      if (match) {
        const result = this.parseRelativeDate(pattern, match, baseDate);
        if (result) {
          logger.debug('Successfully parsed relative date', {
            operation: 'date_parsing',
            metadata: { 
              pattern: pattern.pattern.toString(),
              result: result.date.toISOString(),
              confidence: result.confidence
            }
          });
          return result;
        }
      }
    }

    // Try absolute patterns
    for (const pattern of this.ABSOLUTE_PATTERNS) {
      const match = normalizedText.match(pattern.pattern);
      if (match) {
        const result = this.parseAbsoluteDate(pattern, match, baseDate);
        if (result) {
          logger.debug('Successfully parsed absolute date', {
            operation: 'date_parsing',
            metadata: { 
              pattern: pattern.pattern.toString(),
              result: result.date.toISOString(),
              confidence: result.confidence
            }
          });
          return result;
        }
      }
    }

    logger.debug('Failed to parse date from text', {
      operation: 'date_parsing',
      metadata: { text: normalizedText }
    });

    return null;
  }

  private static parseRelativeDate(pattern: any, match: RegExpMatchArray, baseDate: Date): ParsedDate | null {
    const date = new Date(baseDate);
    let interpretation = '';
    let confidence = 0.9;

    try {
      if (pattern.days !== undefined) {
        date.setDate(date.getDate() + pattern.days);
        interpretation = `${pattern.days} days from base date`;
      } else if (pattern.dayOfWeek !== undefined) {
        const targetDay = pattern.dayOfWeek;
        const currentDay = date.getDay();
        let daysToAdd = targetDay - currentDay;
        
        if (pattern.next || daysToAdd <= 0) {
          daysToAdd += 7;
        }
        
        date.setDate(date.getDate() + daysToAdd);
        interpretation = `Next ${['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][targetDay]}`;
      } else if (pattern.daysFromMatch && match[1]) {
        const days = parseInt(match[1], 10);
        date.setDate(date.getDate() + days);
        interpretation = `${days} days from now`;
      } else if (pattern.months !== undefined) {
        date.setMonth(date.getMonth() + pattern.months);
        interpretation = `${pattern.months} months from base date`;
      }

      // Validate the result
      if (isNaN(date.getTime())) {
        return null;
      }

      return {
        date,
        confidence,
        originalText: match[0],
        interpretation,
      };
    } catch (error) {
      logger.error('Error parsing relative date', {
        operation: 'date_parsing',
        metadata: { pattern: pattern.pattern.toString(), match: match[0] }
      }, error as Error);
      return null;
    }
  }

  private static parseAbsoluteDate(pattern: any, match: RegExpMatchArray, baseDate: Date): ParsedDate | null {
    let date: Date;
    let interpretation = '';
    let confidence = 0.95;

    try {
      if (pattern.format === 'YYYY-MM-DD') {
        date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
        interpretation = 'ISO date format';
      } else if (pattern.format === 'MM/DD/YYYY') {
        date = new Date(parseInt(match[3]), parseInt(match[1]) - 1, parseInt(match[2]));
        interpretation = 'US date format (MM/DD/YYYY)';
      } else if (pattern.format === 'MM/DD/YY') {
        const year = parseInt(match[3]);
        const fullYear = year < 50 ? 2000 + year : 1900 + year;
        date = new Date(fullYear, parseInt(match[1]) - 1, parseInt(match[2]));
        interpretation = 'US date format (MM/DD/YY)';
        confidence = 0.8; // Lower confidence due to year ambiguity
      } else if (pattern.format === 'DD.MM.YYYY') {
        date = new Date(parseInt(match[3]), parseInt(match[2]) - 1, parseInt(match[1]));
        interpretation = 'European date format (DD.MM.YYYY)';
      } else if (pattern.month) {
        const day = parseInt(match[2]);
        const year = match[3] ? parseInt(match[3]) : baseDate.getFullYear();
        date = new Date(year, pattern.month - 1, day);
        interpretation = `Month name format (${match[1]} ${day}, ${year})`;
      } else {
        return null;
      }

      // Validate the result
      if (isNaN(date.getTime())) {
        return null;
      }

      // Check if date is reasonable (not too far in past/future)
      const now = new Date();
      const yearsDiff = Math.abs(date.getFullYear() - now.getFullYear());
      if (yearsDiff > 10) {
        confidence *= 0.5; // Reduce confidence for dates far in future/past
      }

      return {
        date,
        confidence,
        originalText: match[0],
        interpretation,
      };
    } catch (error) {
      logger.error('Error parsing absolute date', {
        operation: 'date_parsing',
        metadata: { pattern: pattern.pattern?.toString(), match: match[0] }
      }, error as Error);
      return null;
    }
  }

  static extractDatesFromText(text: string, baseDate: Date = new Date()): ParsedDate[] {
    const dates: ParsedDate[] = [];
    const words = text.split(/\s+/);
    
    // Try parsing the full text first
    const fullTextDate = this.parseNaturalLanguageDate(text, baseDate);
    if (fullTextDate) {
      dates.push(fullTextDate);
    }

    // Try parsing smaller chunks
    for (let i = 0; i < words.length; i++) {
      for (let j = i + 1; j <= Math.min(i + 5, words.length); j++) {
        const chunk = words.slice(i, j).join(' ');
        const parsedDate = this.parseNaturalLanguageDate(chunk, baseDate);
        if (parsedDate && !dates.some(d => Math.abs(d.date.getTime() - parsedDate.date.getTime()) < 1000)) {
          dates.push(parsedDate);
        }
      }
    }

    return dates.sort((a, b) => b.confidence - a.confidence);
  }

  static formatDateForAI(date: Date): string {
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  }

  static isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  static isDateInPast(dateString: string, baseDate: Date = new Date()): boolean {
    const date = new Date(dateString);
    return date < baseDate;
  }
}
