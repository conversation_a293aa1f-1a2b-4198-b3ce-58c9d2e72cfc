"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader, Send, X, Spark<PERSON>, Calendar, Clock } from "lucide-react";
import { trpc } from "@/utils/trpc";
import { toast } from "sonner";

interface ParsedTask {
  title: string;
  description?: string;
  dueDate?: string;
  estimatedTime?: string;
  priority?: number;
  tags?: string[];
  dependencies?: string[];
}

interface ParsedTasksResponse {
  tasks: ParsedTask[];
  confidence: number;
  reasoning?: string;
  warnings?: string[];
}

interface TaskInputModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTaskCreate?: (tasks: ParsedTask[]) => void; // Optional since tRPC handles persistence
}

export default function TaskInputModal({
  isOpen,
  onClose,
  onTaskCreate
}: TaskInputModalProps) {
  const [input, setInput] = useState("");
  const [parsedResponse, setParsedResponse] = useState<ParsedTasksResponse | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // tRPC mutation for parsing tasks
  const parseTasksMutation = trpc.tasks.parseTasks.useMutation({
    onSuccess: (data: ParsedTasksResponse) => {
      console.log('Tasks parsed successfully:', data);
      setParsedResponse(data);
      setShowPreview(true);

      // Show confidence and warnings
      if (data.confidence < 0.7) {
        toast.warning(`Tasks parsed with low confidence (${Math.round(data.confidence * 100)}%). Please review carefully.`);
      } else {
        toast.success(`Tasks parsed successfully! (${Math.round(data.confidence * 100)}% confidence)`);
      }

      if (data.warnings && data.warnings.length > 0) {
        data.warnings.forEach(warning => {
          toast.warning(warning);
        });
      }
    },
    onError: (error: Error) => {
      console.error('Error parsing tasks:', error);
      toast.error("Failed to parse tasks: " + error.message);

      // Improved fallback with better error handling
      const fallbackTask: ParsedTask = {
        title: input.length > 100 ? input.substring(0, 100) + "..." : input,
        description: "Automatically created from input (AI parsing failed)",
        priority: 3,
        tags: ["fallback"],
      };

      setParsedResponse({
        tasks: [fallbackTask],
        confidence: 0.1,
        warnings: ["AI parsing failed. This task was created automatically from your input."]
      });
      setShowPreview(true);
    },
  });

  const parseNaturalLanguage = (text: string) => {
    console.log('Parsing natural language input:', text);
    parseTasksMutation.mutate({
      input: text,
      saveToDatabase: true,
      includeContext: true,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || parseTasksMutation.isPending) return;

    console.log('Submitting task input:', input.trim());
    parseNaturalLanguage(input.trim());
  };

  const handleConfirm = () => {
    // Since tRPC already handles persistence, we just need to:
    // 1. Optionally call the legacy callback for compatibility
    // 2. Close the modal

    if (onTaskCreate && parsedResponse) {
      onTaskCreate(parsedResponse.tasks);
    }

    const taskCount = parsedResponse?.tasks.length || 0;
    toast.success(`${taskCount} task${taskCount !== 1 ? 's' : ''} created successfully!`);
    handleClose();
  };

  const handleClose = () => {
    setInput("");
    setParsedResponse(null);
    setShowPreview(false);
    onClose();
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return null;
    try {
      return new Date(dateStr).toLocaleDateString();
    } catch {
      return dateStr;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            Add Tasks Naturally
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-4">
          {!showPreview ? (
            <>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Describe what you need to do in natural language:
                </p>
                <div className="text-xs text-muted-foreground space-y-1">
                  <p>• "Finish Stripe onboarding, then email investors tomorrow"</p>
                  <p>• "Schedule dentist appointment for next week"</p>
                  <p>• "Review pull requests and deploy to staging"</p>
                </div>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="relative">
                  <Input
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder="Type your tasks here..."
                    className="pr-12"
                    disabled={parseTasksMutation.isPending}
                    autoFocus
                  />
                  <Button
                    type="submit"
                    size="sm"
                    className="absolute right-1 top-1 h-8 w-8 p-0"
                    disabled={!input.trim() || parseTasksMutation.isPending}
                  >
                    {parseTasksMutation.isPending ? (
                      <Loader className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={!input.trim() || parseTasksMutation.isPending}>
                    {parseTasksMutation.isPending ? "Processing..." : "Parse Tasks"}
                  </Button>
                </div>
              </form>
            </>
          ) : (
            <>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Parsed Tasks</h3>
                  {parsedResponse && (
                    <Badge
                      variant={parsedResponse.confidence >= 0.7 ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {Math.round(parsedResponse.confidence * 100)}% confidence
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  Review and confirm the tasks below:
                </p>

                {parsedResponse?.reasoning && (
                  <div className="p-2 bg-muted rounded text-xs">
                    <strong>AI Reasoning:</strong> {parsedResponse.reasoning}
                  </div>
                )}

                {parsedResponse?.warnings && parsedResponse.warnings.length > 0 && (
                  <div className="p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded text-xs">
                    <strong>Warnings:</strong>
                    <ul className="mt-1 list-disc list-inside">
                      {parsedResponse.warnings.map((warning, idx) => (
                        <li key={idx}>{warning}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div className="space-y-3 max-h-64 overflow-y-auto">
                {parsedResponse?.tasks.map((task, index) => (
                  <Card key={index} className="p-3">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h4 className="font-medium text-sm">{task.title}</h4>
                        {task.priority && (
                          <Badge variant="outline" className="text-xs">
                            Priority {task.priority}
                          </Badge>
                        )}
                      </div>

                      {task.description && (
                        <p className="text-xs text-muted-foreground">{task.description}</p>
                      )}

                      <div className="flex gap-2 text-xs text-muted-foreground">
                        {task.dueDate && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(task.dueDate)}
                          </div>
                        )}
                        {task.estimatedTime && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {task.estimatedTime}
                          </div>
                        )}
                      </div>

                      {task.tags && task.tags.length > 0 && (
                        <div className="flex gap-1 flex-wrap">
                          {task.tags.map((tag, tagIdx) => (
                            <Badge key={tagIdx} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {task.dependencies && task.dependencies.length > 0 && (
                        <div className="text-xs text-muted-foreground">
                          <strong>Depends on:</strong> {task.dependencies.join(', ')}
                        </div>
                      )}
                    </div>
                  </Card>
                ))}
              </div>

              <div className="flex justify-between pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowPreview(false)}
                >
                  Edit Input
                </Button>
                <div className="space-x-2">
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button onClick={handleConfirm}>
                    Create {parsedResponse?.tasks.length || 0} Task{(parsedResponse?.tasks.length || 0) !== 1 ? 's' : ''}
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}